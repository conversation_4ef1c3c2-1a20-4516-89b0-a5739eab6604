#!/usr/bin/env python
"""
<PERSON>rip<PERSON> to run the simplified PINNMamba model training.
"""

import os
import sys
import subprocess

def main():
    """Run the simplified PINNMamba model training."""
    # Define the command to run
    cmd = [
        "python", "train_simple_pinnmamba.py",
        "--in_dim", "3",
        "--out_dim", "1",
        "--hidden_dim", "64",
        "--num_layers", "4",
        "--n_x", "1000",
        "--n_t", "100",
        "--x_radius", "1.0",
        "--t_max", "1.0",
        "--batch_size", "32",
        "--seq_len", "128",
        "--lr", "1e-3",
        "--weight_decay", "1e-5",
        "--epochs", "100",
        "--eval_every", "10",
        "--save_every", "20",
        "--seed", "42",
        "--output_dir", "outputs",
        "--pde_weight", "1.0",
        "--ic_weight", "1.0",
        "--bc_weight", "1.0"
    ]
    
    # Print the command
    print("Running command:")
    print(" ".join(cmd))
    
    # Run the command
    subprocess.run(cmd)

if __name__ == "__main__":
    # Make sure we're in the right conda environment
    if "phd" not in os.environ.get("CONDA_DEFAULT_ENV", ""):
        print("Please activate the 'phd' conda environment before running this script.")
        print("Run: conda activate phd")
        sys.exit(1)
    
    # Run the main function
    main()
