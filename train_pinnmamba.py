"""
Training script for PINNMamba model on the Sine-Gordon equation.
"""

import jax
import jax.numpy as jnp
import optax
import flax
from flax.training import train_state
from functools import partial
import matplotlib.pyplot as plt
from typing import Callable, Dict, Tuple, Any
import argparse
import time
import os
from tqdm import tqdm

from pinnmamba import PINNMamba, create_sine_gordon_data, sine_gordon_pde


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Train PINNMamba model on Sine-Gordon equation')
    
    # Model parameters
    parser.add_argument('--in_dim', type=int, default=3, help='Input dimension (x_dim + 1 for time)')
    parser.add_argument('--out_dim', type=int, default=1, help='Output dimension')
    parser.add_argument('--hidden_dim', type=int, default=64, help='Hidden dimension')
    parser.add_argument('--num_layers', type=int, default=4, help='Number of Mamba layers')
    parser.add_argument('--hidden_d_ff', type=int, default=256, help='Hidden feed-forward dimension')
    parser.add_argument('--heads', type=int, default=2, help='Number of attention heads')
    parser.add_argument('--dropout_rate', type=float, default=0.1, help='Dropout rate')
    
    # Data parameters
    parser.add_argument('--n_x', type=int, default=1000, help='Number of spatial points')
    parser.add_argument('--n_t', type=int, default=100, help='Number of temporal points')
    parser.add_argument('--x_radius', type=float, default=1.0, help='Radius of spatial domain')
    parser.add_argument('--t_max', type=float, default=1.0, help='Maximum time')
    parser.add_argument('--batch_size', type=int, default=32, help='Batch size')
    parser.add_argument('--seq_len', type=int, default=128, help='Sequence length')
    
    # Training parameters
    parser.add_argument('--lr', type=float, default=1e-3, help='Learning rate')
    parser.add_argument('--weight_decay', type=float, default=1e-5, help='Weight decay')
    parser.add_argument('--epochs', type=int, default=100, help='Number of epochs')
    parser.add_argument('--eval_every', type=int, default=10, help='Evaluate every N epochs')
    parser.add_argument('--save_every', type=int, default=20, help='Save model every N epochs')
    parser.add_argument('--seed', type=int, default=42, help='Random seed')
    parser.add_argument('--output_dir', type=str, default='outputs', help='Output directory')
    
    # PDE parameters
    parser.add_argument('--pde_weight', type=float, default=1.0, help='Weight for PDE loss')
    parser.add_argument('--ic_weight', type=float, default=1.0, help='Weight for initial condition loss')
    parser.add_argument('--bc_weight', type=float, default=1.0, help='Weight for boundary condition loss')
    
    return parser.parse_args()


class TrainState(train_state.TrainState):
    """Custom train state with batch statistics."""
    batch_stats: Any = None


def create_train_state(rng, model, learning_rate, weight_decay):
    """Create initial training state."""
    # Create a dummy input for initialization
    batch_size = 2
    seq_len = 4
    x_dim = model.in_dim - 1
    
    x = jnp.ones((batch_size, seq_len, x_dim))
    t = jnp.ones((batch_size, seq_len, 1))
    
    # Initialize model
    variables = model.init(rng, x, t, train=True)
    
    # Create optimizer
    tx = optax.adamw(
        learning_rate=learning_rate,
        weight_decay=weight_decay
    )
    
    # Create training state
    return TrainState.create(
        apply_fn=model.apply,
        params=variables['params'],
        tx=tx,
        batch_stats=variables.get('batch_stats')
    )


def sine_gordon_initial_condition(x, t):
    """
    Initial condition for the Sine-Gordon equation.
    
    For simplicity, we use u(x, 0) = 0 and u_t(x, 0) = 0.
    """
    return jnp.zeros_like(t)


def sine_gordon_boundary_condition(x, t, radius):
    """
    Boundary condition for the Sine-Gordon equation.
    
    For simplicity, we use u(x, t) = 0 for ||x|| = radius.
    """
    # Compute distance from origin
    r = jnp.linalg.norm(x, axis=-1, keepdims=True)
    
    # Apply boundary condition at r = radius
    return jnp.zeros_like(t)


@partial(jax.jit, static_argnames=('model', 'is_training'))
def compute_loss(params, batch_stats, model, x_batch, t_batch, rng, pde_weight, ic_weight, bc_weight, radius, is_training):
    """Compute the loss for a batch."""
    # Define model prediction function
    def u_fn(x, t):
        variables = {'params': params}
        if batch_stats is not None:
            variables['batch_stats'] = batch_stats
        
        return model.apply(
            variables, 
            x, 
            t, 
            train=is_training,
            mutable=['batch_stats'] if is_training else False
        )
    
    # Compute PDE residual
    pde_residual = sine_gordon_pde(x_batch, t_batch, u_fn, rng)
    pde_loss = jnp.mean(pde_residual**2)
    
    # Compute initial condition loss (t=0)
    is_initial = jnp.isclose(t_batch, 0.0)
    if jnp.any(is_initial):
        ic_pred = u_fn(x_batch[is_initial], t_batch[is_initial])
        ic_true = sine_gordon_initial_condition(x_batch[is_initial], t_batch[is_initial])
        ic_loss = jnp.mean((ic_pred - ic_true)**2)
    else:
        ic_loss = 0.0
    
    # Compute boundary condition loss (||x|| = radius)
    r = jnp.linalg.norm(x_batch, axis=-1, keepdims=True)
    is_boundary = jnp.isclose(r, radius, atol=0.05)
    if jnp.any(is_boundary):
        bc_pred = u_fn(x_batch[is_boundary], t_batch[is_boundary])
        bc_true = sine_gordon_boundary_condition(x_batch[is_boundary], t_batch[is_boundary], radius)
        bc_loss = jnp.mean((bc_pred - bc_true)**2)
    else:
        bc_loss = 0.0
    
    # Compute total loss
    total_loss = pde_weight * pde_loss + ic_weight * ic_loss + bc_weight * bc_loss
    
    return total_loss, (pde_loss, ic_loss, bc_loss)


@partial(jax.jit, static_argnames=('model', 'is_training'))
def train_step(state, x_batch, t_batch, rng, pde_weight, ic_weight, bc_weight, radius, model, is_training=True):
    """Perform a single training step."""
    # Define loss function for gradient computation
    def loss_fn(params):
        loss, aux = compute_loss(
            params, 
            state.batch_stats, 
            model, 
            x_batch, 
            t_batch, 
            rng, 
            pde_weight, 
            ic_weight, 
            bc_weight, 
            radius,
            is_training
        )
        return loss, aux
    
    # Compute gradients
    (loss, aux), grads = jax.value_and_grad(loss_fn, has_aux=True)(state.params)
    pde_loss, ic_loss, bc_loss = aux
    
    # Update parameters
    new_state = state.apply_gradients(grads=grads)
    
    return new_state, loss, pde_loss, ic_loss, bc_loss


@partial(jax.jit, static_argnames=('model',))
def eval_step(state, x_batch, t_batch, rng, pde_weight, ic_weight, bc_weight, radius, model):
    """Perform a single evaluation step."""
    loss, (pde_loss, ic_loss, bc_loss) = compute_loss(
        state.params, 
        state.batch_stats, 
        model, 
        x_batch, 
        t_batch, 
        rng, 
        pde_weight, 
        ic_weight, 
        bc_weight, 
        radius,
        False
    )
    
    return loss, pde_loss, ic_loss, bc_loss


def train_model(args):
    """Train the PINNMamba model."""
    # Set random seed
    rng = jax.random.PRNGKey(args.seed)
    rng, init_rng, data_rng = jax.random.split(rng, 3)
    
    # Create model
    model = PINNMamba(
        in_dim=args.in_dim,
        out_dim=args.out_dim,
        hidden_dim=args.hidden_dim,
        num_layer=args.num_layers,
        hidden_d_ff=args.hidden_d_ff,
        heads=args.heads,
        dropout_rate=args.dropout_rate
    )
    
    # Create training state
    state = create_train_state(
        init_rng, 
        model, 
        args.lr, 
        args.weight_decay
    )
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Training loop
    best_loss = float('inf')
    train_losses = []
    eval_losses = []
    
    print(f"Starting training for {args.epochs} epochs...")
    
    for epoch in range(args.epochs):
        start_time = time.time()
        
        # Create training data for this epoch
        rng, data_rng = jax.random.split(data_rng)
        x_batch, t_batch = create_sine_gordon_data(
            n_x=args.n_x,
            n_t=args.n_t,
            x_radius=args.x_radius,
            t_max=args.t_max,
            batch_size=args.batch_size,
            seq_len=args.seq_len
        )
        
        # Training step
        rng, step_rng = jax.random.split(rng)
        state, loss, pde_loss, ic_loss, bc_loss = train_step(
            state, 
            x_batch, 
            t_batch, 
            step_rng, 
            args.pde_weight, 
            args.ic_weight, 
            args.bc_weight, 
            args.x_radius,
            model
        )
        
        train_losses.append(loss.item())
        
        # Evaluation
        if (epoch + 1) % args.eval_every == 0:
            # Create evaluation data
            rng, eval_data_rng = jax.random.split(rng)
            x_eval, t_eval = create_sine_gordon_data(
                n_x=args.n_x,
                n_t=args.n_t,
                x_radius=args.x_radius,
                t_max=args.t_max,
                batch_size=args.batch_size,
                seq_len=args.seq_len
            )
            
            # Evaluation step
            rng, eval_rng = jax.random.split(rng)
            eval_loss, eval_pde_loss, eval_ic_loss, eval_bc_loss = eval_step(
                state, 
                x_eval, 
                t_eval, 
                eval_rng, 
                args.pde_weight, 
                args.ic_weight, 
                args.bc_weight, 
                args.x_radius,
                model
            )
            
            eval_losses.append(eval_loss.item())
            
            # Save best model
            if eval_loss < best_loss:
                best_loss = eval_loss
                # Save model
                model_path = os.path.join(args.output_dir, 'best_model.pkl')
                with open(model_path, 'wb') as f:
                    f.write(flax.serialization.to_bytes(state))
                print(f"Saved best model with loss {best_loss:.6f}")
        
        # Save checkpoint
        if (epoch + 1) % args.save_every == 0:
            checkpoint_path = os.path.join(args.output_dir, f'checkpoint_{epoch+1}.pkl')
            with open(checkpoint_path, 'wb') as f:
                f.write(flax.serialization.to_bytes(state))
        
        # Print progress
        epoch_time = time.time() - start_time
        print(f"Epoch {epoch+1}/{args.epochs} | "
              f"Loss: {loss:.6f} | "
              f"PDE Loss: {pde_loss:.6f} | "
              f"IC Loss: {ic_loss:.6f} | "
              f"BC Loss: {bc_loss:.6f} | "
              f"Time: {epoch_time:.2f}s")
    
    # Save final model
    final_model_path = os.path.join(args.output_dir, 'final_model.pkl')
    with open(final_model_path, 'wb') as f:
        f.write(flax.serialization.to_bytes(state))
    
    # Plot training curve
    plt.figure(figsize=(10, 6))
    plt.plot(train_losses, label='Train Loss')
    plt.plot(range(0, args.epochs, args.eval_every), eval_losses, label='Eval Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.title('Training and Evaluation Loss')
    plt.legend()
    plt.yscale('log')
    plt.grid(True)
    plt.savefig(os.path.join(args.output_dir, 'loss_curve.png'))
    
    print(f"Training completed. Final loss: {loss:.6f}")
    print(f"Best evaluation loss: {best_loss:.6f}")
    print(f"Model saved to {args.output_dir}")


def main():
    """Main function."""
    args = parse_args()
    train_model(args)


if __name__ == "__main__":
    main()
