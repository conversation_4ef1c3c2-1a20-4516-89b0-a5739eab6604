[yapf]
based_on_style = yapf
spaces_before_comment = 2
dedent_closing_brackets = true
column_limit = 80
continuation_indent_width = 2

[flake8]
exclude =
    .git
indent_size = 2
extend-ignore =
    E731
    E124
    E402
    E741
    F722
    F821
max-line-length = 80

[pydocstyle]
convention = google

[isort]
profile = black
multi_line_output = 3
indent = 2
line_length = 80

[mypy]
allow_redefinition = True
check_untyped_defs = True
disallow_incomplete_defs = True
disallow_untyped_defs = True
ignore_missing_imports = True
no_implicit_optional = True
pretty = True
show_error_codes = True
show_error_context = True
show_traceback = True
strict_equality = True
strict_optional = True
warn_no_return = True
warn_redundant_casts = True
warn_unreachable = True
warn_unused_configs = True
warn_unused_ignores = True
