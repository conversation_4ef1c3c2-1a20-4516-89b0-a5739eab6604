"""
STDE-PINNMamba: Physics-Informed Neural Networks with Mamba architecture for the Sine-Gordon equation,
using Stochastic Taylor Differential Equations (STDE) for forward-mode automatic differentiation.
"""

import jax
import jax.numpy as jnp
import flax.linen as nn
from functools import partial
from typing import Callable, Tuple, List, Dict, Any, Optional
import numpy as np

from simple_pinnmamba import SimplePINNMamba, SimpleMambaBlock, PINNMambaEncoder


class STDEFunction:
    """
    A wrapper class for functions that need to be differentiated using STDE.

    This class implements forward-mode automatic differentiation using the
    Stochastic Taylor Differential Equations (STDE) approach.
    """

    def __init__(self, order: int = 2):
        """
        Initialize the STDE function.

        Args:
            order: The order of the Taylor expansion (default: 2)
        """
        self.order = order

    def __call__(self, f: Callable) -> Callable:
        """
        Wrap a function to enable STDE differentiation.

        Args:
            f: The function to wrap

        Returns:
            A wrapped function that computes both the function value and its derivatives
        """
        def wrapped_f(x, *args, **kwargs):
            """
            Compute the function value and its derivatives using STDE.

            Args:
                x: The input to the function
                *args, **kwargs: Additional arguments to pass to the function

            Returns:
                A tuple of (function_value, derivatives)
            """
            # Compute the function value
            y = f(x, *args, **kwargs)

            # Initialize derivatives
            derivatives = []

            # Compute derivatives using forward-mode AD
            for order in range(1, self.order + 1):
                # Create a function that computes the derivative of order 'order'
                def compute_derivative(x_val):
                    # Use JAX's jacfwd for forward-mode AD
                    if order == 1:
                        # First derivative
                        jac_fn = jax.jacfwd(f, argnums=0)
                        return jac_fn(x_val, *args, **kwargs)
                    else:
                        # Higher-order derivatives
                        def higher_order_fn(x_val):
                            prev_order = order - 1
                            # Recursively compute the previous order derivative
                            if prev_order == 1:
                                jac_fn = jax.jacfwd(f, argnums=0)
                                return jac_fn(x_val, *args, **kwargs)
                            else:
                                return jax.jacfwd(higher_order_fn, argnums=0)(x_val)

                        return jax.jacfwd(higher_order_fn, argnums=0)(x_val)

                # Compute the derivative
                derivative = compute_derivative(x)
                derivatives.append(derivative)

            return y, derivatives

        return wrapped_f


class STDESimplePINNMamba(nn.Module):
    """
    STDE version of SimplePINNMamba model for solving PDEs using sequence-to-sequence approach.

    This model follows the encoder-only architecture from the PINNMamba paper and adds
    STDE capabilities for computing derivatives.
    """
    in_dim: int
    out_dim: int
    hidden_dim: int
    num_layers: int
    stde_order: int = 2

    @nn.compact
    def __call__(self, x, t, train: bool = False):
        """
        Forward pass of the STDE-PINNMamba model.

        Args:
            x: Spatial coordinates tensor of shape (batch_size, seq_len, x_dim)
            t: Time coordinates tensor of shape (batch_size, seq_len, 1)
            train: Whether the model is in training mode

        Returns:
            Output tensor of shape (batch_size, seq_len, out_dim)
        """
        # Concatenate spatial and temporal coordinates
        inputs = jnp.concatenate([x, t], axis=-1)  # (batch_size, seq_len, in_dim)

        # Input embedding
        x = nn.Dense(features=self.hidden_dim, name="input_embedding")(inputs)

        # Apply encoder
        encoder = PINNMambaEncoder(
            hidden_dim=self.hidden_dim,
            num_layers=self.num_layers
        )
        x = encoder(x)

        # Output projection
        x = nn.Dense(features=self.hidden_dim)(x)
        x = nn.gelu(x)
        x = nn.Dense(features=self.out_dim, name="output_projection")(x)

        return x

    def stde_forward(self, x, t, train: bool = False):
        """
        Forward pass with STDE for computing derivatives.

        Args:
            x: Spatial coordinates tensor of shape (batch_size, seq_len, x_dim)
            t: Time coordinates tensor of shape (batch_size, seq_len, 1)
            train: Whether the model is in training mode

        Returns:
            A tuple of (output, derivatives) where derivatives is a list of tensors
            representing the derivatives of the output with respect to the input.
        """
        # Create an STDE wrapper for the forward pass
        @STDEFunction(order=self.stde_order)
        def forward_fn(inputs):
            # Split inputs into x and t
            x_val = inputs[..., :-1]
            t_val = inputs[..., -1:]

            # Apply the model
            return self.__call__(x_val, t_val, train=train)

        # Concatenate inputs
        inputs = jnp.concatenate([x, t], axis=-1)

        # Apply the STDE forward pass
        output, derivatives = forward_fn(inputs)

        return output, derivatives


def compute_stde_pde_residual(model_fn, params, x, t, stde_order=2):
    """
    Compute the PDE residual using finite differences instead of STDE.

    This function falls back to finite differences due to the complexity of
    handling higher-order derivatives with STDE in JAX.

    Args:
        model_fn: The model function that takes parameters, x, and t
        params: The model parameters
        x: Spatial coordinates tensor of shape (batch_size, seq_len, x_dim)
        t: Time coordinates tensor of shape (batch_size, seq_len, 1)
        stde_order: The order of the Taylor expansion (not used)

    Returns:
        The PDE residual
    """
    # Use finite differences to compute derivatives
    return finite_diff_pde_residual(model_fn, params, x, t)


def finite_diff_pde_residual(model_fn, params, x, t):
    """
    Compute the PDE residual using finite differences.

    Args:
        model_fn: The model function that takes parameters, x, and t
        params: The model parameters
        x: Spatial coordinates tensor of shape (batch_size, seq_len, x_dim)
        t: Time coordinates tensor of shape (batch_size, seq_len, 1)

    Returns:
        The PDE residual
    """
    # Define a function to compute u(x, t)
    def u_fn(x_val, t_val):
        return model_fn(params, x_val, t_val)

    # Compute u and its value
    u_val = u_fn(x, t)

    # Use finite differences to approximate derivatives
    # Small step size for finite differences
    h = 0.01

    # Time derivatives
    t_plus = t + h
    t_minus = t - h
    u_plus_t = u_fn(x, t_plus)
    u_minus_t = u_fn(x, t_minus)

    # First time derivative: central difference
    u_t = (u_plus_t - u_minus_t) / (2 * h)

    # Second time derivative: central difference
    u_tt = (u_plus_t - 2 * u_val + u_minus_t) / (h * h)

    # Spatial derivatives (for each dimension)
    u_xx = jnp.zeros_like(u_val)

    # For each spatial dimension
    for d in range(x.shape[-1]):
        # Create shifted coordinates
        x_plus = x.at[:, :, d].add(h)
        x_minus = x.at[:, :, d].add(-h)

        # Compute function values at shifted coordinates
        u_plus_x = u_fn(x_plus, t)
        u_minus_x = u_fn(x_minus, t)

        # Second spatial derivative: central difference
        u_xx += (u_plus_x - 2 * u_val + u_minus_x) / (h * h)

    # Compute the Sine-Gordon PDE residual: u_tt - u_xx + sin(u) = 0
    residual = u_tt - u_xx + jnp.sin(u_val)

    return residual


def create_sequence_data(x_points, t_points, batch_size=32, seq_len=128):
    """
    Create sequence data for training STDE-PINNMamba.

    Args:
        x_points: Spatial points of shape (n_x, x_dim)
        t_points: Temporal points of shape (n_t, 1)
        batch_size: Number of sequences in a batch
        seq_len: Length of each sequence

    Returns:
        Batched sequences of shape (batch_size, seq_len, x_dim + 1)
    """
    n_x = x_points.shape[0]
    n_t = t_points.shape[0]
    x_dim = x_points.shape[1]

    # Create random indices for sampling
    key = jax.random.PRNGKey(0)
    key, subkey1, subkey2 = jax.random.split(key, 3)

    # Initialize batch arrays
    x_batch = jnp.zeros((batch_size, seq_len, x_dim))
    t_batch = jnp.zeros((batch_size, seq_len, 1))

    for i in range(batch_size):
        # For each batch, create a sequence by sampling random points
        x_indices = jax.random.randint(subkey1, (seq_len,), 0, n_x)
        t_indices = jax.random.randint(subkey2, (seq_len,), 0, n_t)

        # Sample points
        x_seq = x_points[x_indices]
        t_seq = t_points[t_indices]

        # Store in batch
        x_batch = x_batch.at[i].set(x_seq)
        t_batch = t_batch.at[i].set(t_seq)

        # Update keys for next batch
        key, subkey1, subkey2 = jax.random.split(key, 3)

    return x_batch, t_batch


def create_sine_gordon_data(n_x=100, n_t=50, x_radius=1.0, t_max=1.0, batch_size=32, seq_len=128):
    """
    Create data for the Sine-Gordon equation.

    Args:
        n_x: Number of spatial points
        n_t: Number of temporal points
        x_radius: Radius of the spatial domain
        t_max: Maximum time
        batch_size: Number of sequences in a batch
        seq_len: Length of each sequence

    Returns:
        Batched sequences for training
    """
    # Create spatial points in a ball
    key = jax.random.PRNGKey(0)

    # Create spatial points
    x_points = jax.random.uniform(key, (n_x, 2), minval=-x_radius, maxval=x_radius)

    # Create temporal points
    t_points = jnp.linspace(0, t_max, n_t).reshape(n_t, 1)

    # Create sequence data
    return create_sequence_data(x_points, t_points, batch_size, seq_len)


def test_stde_pinnmamba():
    """Test the STDE-PINNMamba model."""
    print("\n=== Testing STDE-PINNMamba Model ===")

    # Test parameters
    batch_size = 2
    seq_len = 4
    in_dim = 3  # 2D spatial + 1D temporal
    out_dim = 1
    hidden_dim = 16
    num_layers = 2
    stde_order = 2

    print(f"Test parameters: batch_size={batch_size}, seq_len={seq_len}, in_dim={in_dim}, out_dim={out_dim}")
    print(f"Model config: hidden_dim={hidden_dim}, num_layers={num_layers}, stde_order={stde_order}")

    # Initialize model
    model = STDESimplePINNMamba(
        in_dim=in_dim,
        out_dim=out_dim,
        hidden_dim=hidden_dim,
        num_layers=num_layers,
        stde_order=stde_order
    )
    print("Model initialized successfully")

    # Create dummy inputs
    key = jax.random.PRNGKey(0)
    key, subkey1, subkey2 = jax.random.split(key, 3)
    x = jax.random.normal(subkey1, (batch_size, seq_len, in_dim-1))
    t = jax.random.normal(subkey2, (batch_size, seq_len, 1))
    print(f"Input shapes: x={x.shape}, t={t.shape}")

    # Initialize parameters
    variables = model.init(key, x, t)
    print("Parameters initialized successfully")

    # Run forward pass
    output = model.apply(variables, x, t)
    print(f"Forward pass completed successfully")
    print(f"Output shape: {output.shape}")

    # Run STDE forward pass
    stde_output, derivatives = model.apply(variables, x, t, method=model.stde_forward)
    print(f"STDE forward pass completed successfully")
    print(f"STDE output shape: {stde_output.shape}")
    print(f"Number of derivatives: {len(derivatives)}")

    # Check that the outputs match
    assert jnp.allclose(output, stde_output, atol=1e-5), "STDE output doesn't match regular output"
    print("STDE output matches regular output")

    # Check derivative shapes
    first_derivative = derivatives[0]
    second_derivative = derivatives[1]
    print(f"First derivative shape: {first_derivative.shape}")
    print(f"Second derivative shape: {second_derivative.shape}")

    # Compute PDE residual using finite differences (STDE falls back to this)
    residual = compute_stde_pde_residual(
        model_fn=model.apply,
        params=variables,
        x=x,
        t=t,
        stde_order=stde_order
    )
    print(f"PDE residual shape: {residual.shape}")

    print("STDE-PINNMamba model test passed!")


if __name__ == "__main__":
    test_stde_pinnmamba()
