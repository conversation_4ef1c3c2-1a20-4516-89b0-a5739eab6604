# STDE-Compatible Mamba for Sine-Gordon Equation

This repository contains an implementation of a Physics-Informed Neural Network (PINN) using a STDE-compatible Mamba model for solving the Sine-Gordon equation.

## Overview

The Sine-Gordon equation is a nonlinear hyperbolic partial differential equation that appears in various areas of physics, including quantum field theory, nonlinear optics, and condensed matter physics. The equation is given by:

$$\nabla^2 u + \sin(u) = 0$$

where $u$ is the unknown function and $\nabla^2$ is the Laplacian operator.

This implementation uses a STDE-compatible Mamba model to solve the Sine-Gordon equation. The Stochastic Taylor Derivative Estimator (STDE) is a technique for estimating derivatives in a Physics-Informed Neural Network (PINN) framework. It relies on JAX's `jax.experimental.jet` module, which provides forward-mode automatic differentiation using Taylor series expansions.

## Files

- `stde_mamba.py`: Contains the STDE-compatible Mamba implementation.
- `mamba_utils.py`: Provides utility functions for working with Mamba models in a PINN framework.
- `stde_sine_gordon.py`: Contains the PINN implementation for the Sine-Gordon equation using the STDE-compatible Mamba model.
- `run_stde_sine_gordon.py`: Script to run the STDE-compatible Mamba model on the Sine-Gordon equation.

## Usage

### Prerequisites

Make sure you have the `phd` conda environment activated:

```bash
conda activate phd
```

### Running the Example

To run the example, execute:

```bash
python run_stde_sine_gordon.py
```

This will train a PINN using the STDE-compatible Mamba model to solve the Sine-Gordon equation.

### Customizing the Model

You can customize the model by modifying the command-line arguments in `run_stde_sine_gordon.py`. Here are some of the available options:

- `--hidden_features`: Number of hidden features in the Mamba model.
- `--expansion_factor`: Expansion factor for the Mamba model.
- `--dt_rank`: Rank for the dt projection in the Mamba model.
- `--activation`: Activation function to use (gelu or silu).
- `--norm_type`: Normalization type to use (layer or rms).
- `--dense_expansion`: Expansion factor for the dense layers.
- `--complement`: Whether to use complement mode.
- `--tie_in_proj`: Whether to tie input projections.
- `--tie_gate`: Whether to tie gates.
- `--concatenate_fwd_rev`: Whether to concatenate forward and reverse outputs.
- `--N_f`: Number of residual points.
- `--N_test`: Number of test points.
- `--test_batch_size`: Batch size for testing.
- `--x_radius`: Radius of the domain.
- `--rand_batch_size`: Batch size for random sampling.
- `--eval_every`: Evaluate every N epochs.

## Implementation Details

The implementation consists of the following components:

1. A STDE-compatible Mamba model that avoids using `lax.scan`, which is not supported by `jax.experimental.jet`.
2. A PINN framework that uses the STDE-compatible Mamba model to solve the Sine-Gordon equation.
3. A training loop that optimizes the PINN to satisfy the Sine-Gordon equation.

The current implementation provides a simplified version of the Mamba model that is compatible with jax.experimental.jet. The full SSM scan operation is not yet fully implemented in a way that works with jet, but the simplified model provides a starting point for using Mamba-like architectures with Taylor automatic differentiation.

## References

- [Mamba: Linear-Time Sequence Modeling with Selective State Spaces](https://arxiv.org/abs/2312.00752)
- [JAX: Automatic Differentiation in Python](https://github.com/google/jax)
- [JAX Experimental Jet Module](https://jax.readthedocs.io/en/latest/jax.experimental.jet.html)
- [Physics-Informed Neural Networks](https://arxiv.org/abs/1711.10561)
