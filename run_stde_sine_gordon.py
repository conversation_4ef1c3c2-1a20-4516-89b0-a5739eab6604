#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to run the STDE-compatible Mamba model on the Sine-Gordon equation.

This script demonstrates how to use the STDE-compatible Mamba model
in a Physics-Informed Neural Network (PINN) framework for solving
the Sine-Gordon equation.
"""

import os
import sys
import subprocess

def main():
    """Run the STDE-compatible Mamba model on the Sine-Gordon equation."""
    # Define the command to run
    cmd = [
        "python", "stde_sine_gordon.py",
        "--SEED", "42",
        "--dim", "10",
        "--epochs", "1000",
        "--lr", "1e-3",
        "--hidden_features", "32",
        "--expansion_factor", "2.0",
        "--dt_rank", "auto",
        "--activation", "gelu",
        "--norm_type", "layer",
        "--dense_expansion", "4",
        "--complement",
        "--tie_in_proj",
        "--tie_gate",
        "--concatenate_fwd_rev",
        "--N_f", "100",
        "--N_test", "1000",
        "--test_batch_size", "100",
        "--x_radius", "1.0",
        "--rand_batch_size", "10",
        "--eval_every", "100"
    ]
    
    # Print the command
    print("Running command:")
    print(" ".join(cmd))
    
    # Run the command
    subprocess.run(cmd)

if __name__ == "__main__":
    # Make sure we're in the right conda environment
    if "phd" not in os.environ.get("CONDA_DEFAULT_ENV", ""):
        print("Please activate the 'phd' conda environment before running this script.")
        print("Run: conda activate phd")
        sys.exit(1)
    
    # Run the main function
    main()
