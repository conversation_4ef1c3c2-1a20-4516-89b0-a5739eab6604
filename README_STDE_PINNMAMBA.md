# STDE-PINNMamba for Sine-Gordon Equation

This repository contains an implementation of Physics-Informed Neural Networks with Mamba architecture (STDE-PINNMamba) for solving the Sine-Gordon equation. The implementation uses Stochastic Taylor Differential Equations (STDE) for forward-mode automatic differentiation.

## Overview

The Sine-Gordon equation is a nonlinear hyperbolic partial differential equation that appears in various areas of physics, including quantum field theory, nonlinear optics, and condensed matter physics. The equation is given by:

$$u_{tt} - u_{xx} + \sin(u) = 0$$

where $u$ is the unknown function, $u_{tt}$ is the second derivative with respect to time, and $u_{xx}$ is the second derivative with respect to space.

This implementation uses a STDE-PINNMamba model to solve the Sine-Gordon equation. The STDE-PINNMamba architecture combines the strengths of Physics-Informed Neural Networks (PINNs) with the Mamba architecture and uses STDE for computing derivatives.

## Files

- `stde_pinnmamba.py`: Contains the STDE-PINNMamba model implementation and utility functions for creating sequence data.
- `train_stde_pinnmamba.py`: Contains the training script for the STDE-PINNMamba model.
- `run_stde_pinnmamba.py`: Script to run the STDE-PINNMamba model training.
- `run_stde_pinnmamba_tests.py`: Script to run the STDE-PINNMamba model tests.

## Usage

### Prerequisites

Make sure you have the `phd` conda environment activated:

```bash
conda activate phd
```

### Running the Tests

To run the STDE-PINNMamba model tests, execute:

```bash
python run_stde_pinnmamba_tests.py
```

This will test the STDE-PINNMamba model implementation and the STDE differentiation.

### Training the Model

To train the STDE-PINNMamba model on the Sine-Gordon equation, execute:

```bash
python run_stde_pinnmamba.py
```

This will train the model with the default parameters and save the results to the `outputs_stde` directory.

### Customizing the Training

You can customize the training by modifying the parameters in `run_stde_pinnmamba.py` or by directly running `train_stde_pinnmamba.py` with custom arguments:

```bash
python train_stde_pinnmamba.py --hidden_dim 128 --num_layers 6 --epochs 200 --use_stde
```

## Model Architecture

The STDE-PINNMamba model consists of the following components:

1. **Input Embedding**: Projects the input coordinates (spatial and temporal) to a higher-dimensional space.
2. **Encoder**: Processes the embedded input using multiple layers of SimpleMambaBlock.
3. **Output Projection**: Projects the encoded representation to the output dimension.
4. **STDE Differentiation**: Uses forward-mode automatic differentiation with Taylor expansion to compute derivatives.

The SimpleMambaBlock is a simplified version of the Mamba architecture that includes:
- Layer normalization
- Dense projection to higher dimension
- Sequence modeling with an attention-like mechanism
- Residual connection

This implementation does not use dropout, making it more compatible with JAX's tracer system and simplifying the training process. While it provides an STDE implementation for forward-mode automatic differentiation, it falls back to finite differences for computing PDE derivatives due to the complexity of handling higher-order derivatives with STDE in JAX.

## Analytical Solution

For evaluation and visualization purposes, the implementation includes an analytical solution for the Sine-Gordon equation. The analytical solution used is a kink soliton solution:

$$u(x, t) = 4 \arctan(\exp(\gamma (x - vt)))$$

where $\gamma = 1/\sqrt{1-v^2}$ is the Lorentz factor and $v$ is the velocity of the soliton.

## Visualization

The training script includes functionality to visualize the true vs predicted solution:

1. **Solution Comparison**: Plots the true solution, predicted solution, and a comparison of both at different time points.
2. **Error Plot**: Plots the absolute error between the true and predicted solutions at different time points.

These visualizations are generated:
- At the end of training
- Whenever a new best model is found during training

The plots are saved in the output directory specified by the `--output_dir` argument.

The model is trained to satisfy:
- The Sine-Gordon PDE: $u_{tt} - u_{xx} + \sin(u) = 0$
- Initial conditions: $u(x, 0) = 0$ and $u_t(x, 0) = 0$
- Boundary conditions: $u(x, t) = 0$ for $\|x\| = \text{radius}$

## STDE Implementation

The STDE implementation uses JAX's forward-mode automatic differentiation to compute derivatives of arbitrary order. The key components are:

1. **STDEFunction**: A wrapper class that computes both the function value and its derivatives using forward-mode automatic differentiation.
2. **stde_forward**: A method in the STDESimplePINNMamba class that applies the STDE wrapper to the forward pass.

While the STDE approach allows for more accurate computation of derivatives compared to finite differences, especially for higher-order derivatives, the current implementation falls back to finite differences for computing PDE derivatives due to the complexity of handling higher-order derivatives with STDE in JAX. This is a practical compromise that allows the model to train efficiently while still demonstrating the STDE approach for forward-mode automatic differentiation.

## Parameters

The model and training parameters can be customized through command-line arguments:

### Model Parameters
- `--in_dim`: Input dimension (x_dim + 1 for time)
- `--out_dim`: Output dimension
- `--hidden_dim`: Hidden dimension
- `--num_layers`: Number of Mamba layers
- `--stde_order`: Order of STDE Taylor expansion
- `--use_stde`: Use STDE for computing derivatives (flag)

### Data Parameters
- `--n_x`: Number of spatial points
- `--n_t`: Number of temporal points
- `--x_radius`: Radius of spatial domain
- `--t_max`: Maximum time
- `--batch_size`: Batch size
- `--seq_len`: Sequence length

### Training Parameters
- `--lr`: Learning rate
- `--weight_decay`: Weight decay
- `--epochs`: Number of epochs
- `--eval_every`: Evaluate every N epochs
- `--save_every`: Save model every N epochs
- `--seed`: Random seed
- `--output_dir`: Output directory

### PDE Parameters
- `--pde_weight`: Weight for PDE loss
- `--ic_weight`: Weight for initial condition loss
- `--bc_weight`: Weight for boundary condition loss

## References

- [PINNMamba: Physics-Informed Neural Networks with Mamba Architecture](https://arxiv.org/abs/2403.01131)
- [Mamba: Linear-Time Sequence Modeling with Selective State Spaces](https://arxiv.org/abs/2312.00752)
- [Physics-Informed Neural Networks](https://arxiv.org/abs/1711.10561)
- [Stochastic Taylor Differential Equations](https://arxiv.org/abs/2201.05637)
