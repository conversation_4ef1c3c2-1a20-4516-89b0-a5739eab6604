#!/usr/bin/env python
import argparse
from functools import partial
from typing import Callable, Named<PERSON><PERSON><PERSON>, <PERSON><PERSON>

import jax
import jax.numpy as jnp
from jax.experimental import jet
from jax import lax
from jaxtyping import Array, Float
from jax import config
config.update("jax_enable_x64", True)
import numpy as np

from flax.training import train_state
import optax
from tqdm import tqdm

import matplotlib.pyplot as plt
from pprint import pprint

from my_mamba import BidirectionalMamba, MambaConfig, DiagnosticsConfig

# -----------------------------------------------------------------------------
# CLI arguments
# -----------------------------------------------------------------------------
parser = argparse.ArgumentParser(description="PINN Training with Bi-MAMBA")

# -- existing args --
parser.add_argument("--SEED", type=int, default=0)
parser.add_argument("--dim", type=int, default=2, help="spatial dimension of the problem")
parser.add_argument("--epochs", type=int, default=4000)
parser.add_argument("--eval_every", type=int, default=1000)
parser.add_argument("--lr", type=float, default=1e-3)
parser.add_argument("--N_f", type=int, default=10, help="number of collocation points per batch")
parser.add_argument("--N_test", type=int, default=2000)
parser.add_argument("--test_batch_size", type=int, default=20)
parser.add_argument("--seq_len", type=int, default=5, help="sequence length for Bi-MAMBA")
parser.add_argument("--rand_batch_size", type=int, default=10)
parser.add_argument("--x_radius", type=float, default=1.0)
parser.add_argument(
    "--x_ordering", type=str, choices=["none", "coordinate", "radial"], default="radial",
    help="How to order your spatial sequence: `none` (leave random), "
         "`coordinate` (sort by x[0]), `radial` (sort by ∥x∥)."
)
parser.add_argument(
    "--sampling_mode", type=str,
    choices=["random", "grid", "radial_segments", "arcs"],
    default="random",
    help="How to sample points: `random`=Monte‐Carlo; `grid`=even grid; "
         "`radial_segments`=random‐start radial lines; `arcs`=random curved arcs."
)

parser.add_argument(
    '--sparse', action=argparse.BooleanOptionalAction, default=True,
    help='whether to use sparse or dense stde'
)

# -- arguments for MambaConfig --
parser.add_argument("--hidden_features",    type=int,    default=16,      help="hidden_features in each Mamba block")
parser.add_argument("--expansion_factor",   type=float,  default=2.0,     help="expansion factor in Mamba MLP")
parser.add_argument("--dt_rank",            type=str,    default="auto", choices=["auto","full","low"], help="dt_rank setting")
parser.add_argument("--activation",         type=str,    default="tanh",  choices=["silu","relu","gelu","tanh"], help="activation fn")
parser.add_argument("--norm_type",          type=str,    default="none",  choices=["none","batch","layer"], help="type of normalization")
parser.add_argument(
    "--mlp_layer", action="store_true", default=True,
    help="whether to include an extra MLP layer after Mamba blocks"
)
parser.add_argument("--dense_expansion",    type=int,    default=2,       help="dense expansion ratio")
parser.add_argument("--complement",         action="store_true", default=False, help="use complement flag")
parser.add_argument("--tie_in_proj",        action="store_true", default=False, help="tie input projection weights")
parser.add_argument("--tie_gate",           action="store_true", default=False, help="tie gating weights")
parser.add_argument(
    "--concatenate_fwd_rev", action="store_true", default=False,
    help="concatenate forward & reverse outputs instead of summing"
)

# -- arguments for DiagnosticsConfig --
parser.add_argument("--diag_skip",     action="store_true", help="enable skip diagnostics")
parser.add_argument("--diag_gate",     action="store_true", help="enable gate diagnostics")
parser.add_argument("--diag_gated",    action="store_true", help="enable gated diagnostics")
parser.add_argument("--diag_residual", action="store_true", help="enable residual diagnostics")

args = parser.parse_args()
pprint(args)


np.random.seed(args.SEED)

# -----------------------------------------------------------------------------
# Domain sampler: can return single points or time sequences of points
# -----------------------------------------------------------------------------

@partial(jax.jit, static_argnames=(
    "batch_size","seq_len","radius","dim",
    "sampling_mode","x_ordering"
))
def sample_domain_fn(batch_size: int,
                     rng: jax.Array,
                     radius: float,
                     dim: int,
                     seq_len: int,
                     sampling_mode: str = "random",
                     x_ordering:   str = "none",
                     ) -> Tuple[jnp.ndarray, jax.Array]:
    # 1) Sampling
    if sampling_mode == "random":
        # your Monte‐Carlo interior sampler
        keys = jax.random.split(rng, seq_len + 1)
        out = []
        for i in range(seq_len):
            r = jax.random.uniform(keys[i], (batch_size, 1), minval=0.0, maxval=radius)
            x = jax.random.normal(keys[i+1], (batch_size, dim))
            x = x / jnp.linalg.norm(x, axis=-1, keepdims=True) * r
            out.append(x)
        x_seq, new_rng = jnp.stack(out, axis=1), keys[0]

    elif sampling_mode == "grid":
        # even grid projected onto sphere
        n = int(round(batch_size ** (1/ dim)))
        coords = jnp.linspace(-radius, radius, n)
        meshes = jnp.meshgrid(*([coords]*dim), indexing="ij")
        pts = jnp.stack([m.flatten() for m in meshes], axis=-1)  # (B, D)
        norm = jnp.linalg.norm(pts, axis=-1, keepdims=True)
        pts = pts / norm * radius
        x_seq = jnp.broadcast_to(pts[:,None,:], (batch_size, seq_len, dim))
        new_rng = rng

    elif sampling_mode == "radial_segments":
        # Random‐start & end radial segments:
        # each sequence is a straight ray from r_start → r_end, evenly spaced.

        # 1) split RNG for three draws
        rng, sub_r0, sub_r1, sub_dir = jax.random.split(rng, 4)

        # 2) sample two radii ∈ [0, R]
        r0 = jax.random.uniform(sub_r0, (batch_size,1), minval=0.0, maxval=radius/4)
        r1 = jax.random.uniform(sub_r1, (batch_size,1), minval=radius, maxval=radius)

        # 3) sort so r_start ≤ r_end
        r_start = jnp.minimum(r0, r1)   # shape (B,1)
        r_end   = jnp.maximum(r0, r1)   # shape (B,1)

        # 4) create L linearly spaced steps in [0,1]
        t = jnp.linspace(0.0, 1.0, seq_len).reshape(1, seq_len, 1)  # (1, L, 1)

        # 5) compute per‐step radii: r = (1−t)·r_start + t·r_end
        radii = (1.0 - t) * r_start[:,None,:] + t * r_end[:,None,:]  # (B, L, 1)

        # 6) sample random directions on unit-sphere
        dirs = jax.random.normal(sub_dir, (batch_size, dim))
        dirs = dirs / jnp.linalg.norm(dirs, axis=-1, keepdims=True)  # (B, D)

        # 7) assemble sequences: x_seq[b,i,:] = radii[b,i,0] * dirs[b,:]
        x_seq, new_rng = dirs[:, None, :] * radii, rng  # → (B, L, D)
    elif sampling_mode == "arcs":
        # Random curved “arc” sequences in the ball
        # split RNG for all sub‑draws
        rng, sub_r, sub_a, sub_b, sub_phi0, sub_dphi = jax.random.split(rng, 6)

        # 1) pick a radius for the circle each arc lies on
        r0 = jax.random.uniform(sub_r, (batch_size,1),
                                minval=0.0, maxval=radius)   # (B,1)

        # 2) pick a random 2D plane via two random vectors a,b
        a = jax.random.normal(sub_a, (batch_size, dim))      # (B,D)
        b = jax.random.normal(sub_b, (batch_size, dim))      # (B,D)
        # orthonormalize: u = a/||a||, v = (b − (u⋅b)u)/||…||
        u = a / jnp.linalg.norm(a, axis=-1, keepdims=True)
        proj = jnp.sum(b * u, axis=-1, keepdims=True) * u
        v_raw = b - proj
        v = v_raw / jnp.linalg.norm(v_raw, axis=-1, keepdims=True)  # (B,D)

        # 3) choose a start angle φ₀ ∈ [0,2π) and Δφ ∈ [−π/2,π/2]
        φ0 = jax.random.uniform(sub_phi0, (batch_size,1),
                                minval=0.0, maxval=2*jnp.pi)       # (B,1)
        dφ = jax.random.uniform(sub_dphi, (batch_size,1),
                                minval=-jnp.pi/2, maxval=jnp.pi/2)  # (B,1)
        φ1 = φ0 + dφ                                             # (B,1)

        # 4) interpolate L angles from φ₀→φ₁
        t = jnp.linspace(0.0, 1.0, seq_len).reshape(1, seq_len, 1)   # (1,L,1)
        angles = φ0[:, None, :] + t * (φ1 - φ0)                     # (B,L,1)

        # 5) build points: x = r0 * (cos(θ) u + sin(θ) v)
        cosθ = jnp.cos(angles)
        sinθ = jnp.sin(angles)
        directions = cosθ * u[:, None, :] + sinθ * v[:, None, :]    # (B,L,D)
        x_seq, new_rng = directions * r0[:, None, :], rng           # (B,L,D)

        return x_seq, new_rng



    else:
        raise ValueError(f"Unknown sampling_mode: {sampling_mode}")

    # 2) Ordering
    if x_ordering == "coordinate":
        idx = jnp.argsort(x_seq[...,0], axis=1)
    elif x_ordering == "radial":
        idx = jnp.argsort(jnp.linalg.norm(x_seq, axis=-1), axis=1)
    elif x_ordering == "none":
        return x_seq, new_rng
    else:
        raise ValueError(f"Unknown x_ordering: {x_ordering}")

    batch_idx = jnp.arange(batch_size)[:,None]
    x_seq = x_seq[batch_idx, idx]
    return x_seq, new_rng

# -----------------------------------------------------------------------------
# Hessian‐trace estimator
# -----------------------------------------------------------------------------

# STDE
def hess_trace(fn: Callable) -> Callable:

    def fn_trace(x_i, key):
        key, subkey = jax.random.split(key)

        if args.sparse:
            key, subkey = jax.random.split(subkey)
            idx_set = jax.random.choice(
                subkey, args.dim, shape=(args.rand_batch_size,), replace=True # False from author
            )
            rand_vec = jax.vmap(lambda i: jnp.eye(args.dim)[i])(idx_set)

        else:
            key, subkey = jax.random.split(subkey)
            rand_vec = 2 * (
                jax.random.randint(
                subkey, shape=(args.rand_batch_size, args.dim), minval=0, maxval=2
                ) - 0.5
            )
        # perform the jvp‐via‐Taylor‐series
        taylor_2 = lambda v: jet.jet(
        fun=fn, primals=(x_i,), series=((v, jnp.zeros(args.dim)),)
        )
        f_vals, (_, hvps) = jax.vmap(taylor_2)(rand_vec)
        trace_est = jnp.mean(hvps)
        if args.sparse:
            trace_est *= args.dim
        return f_vals[0], trace_est, key

    return fn_trace

def test_hess_trace_estimator():
    print("\n=== Testing STDE Hessian-Trace Estimator ===")
    key = jax.random.PRNGKey(args.SEED)

    # simple test function f(x) = sum(x^2)  ⇒  Hessian = 2 I  ⇒  trace = 2 * D
    D = args.dim
    x = jnp.linspace(1.0, 2.0, D)

    def f_fn(xi):
        return jnp.sum(xi**2)

    # assume you have hess_trace(fn)(x, key) -> (f(x), trace_estimate, new_key)
    ht = hess_trace(f_fn)
    fval, trace_est, _ = ht(x, key)

    exact = 2.0 * D
    print(f"  f(x) = {fval:.4f}, exact trace = {exact:.4f}, STDE estimate = {trace_est:.4f}")

    # allow, say, 5% relative error on the Monte-Carlo estimate
    assert jnp.allclose(trace_est, exact, rtol=0.05), (
        f"STDE trace {trace_est} differs from exact {exact}"
    )
    print("STDE Hessian-Trace test passed!")



# # Simple HTE
# def hess_trace(fn: Callable) -> Callable:
#     """
#     Returns a function that, given x_i and an RNG key, returns
#       ( f(x_i), trace(H_f)(x_i), next_key ).
#     The trace is estimated via Hutchinson’s method using jax.jvp.
#     """
#     grad_fn = jax.grad(fn)

#     def fn_trace(x_i, key):
#         # split off a subkey for randomness
#         key, subkey = jax.random.split(key)

#         # draw a random Rademacher vector v
#         # v = jax.random.rademacher(subkey, shape=x_i.shape)
#         v = jax.random.normal(subkey, shape=x_i.shape)

#         # compute H_f · v via jvp of the gradient
#         hvp = jax.jvp(grad_fn, (x_i,), (v,))[1]

#         # Hutchinson trace estimate: E[vᵀ(Hv)] = trace(H)
#         trace_est = jnp.vdot(v, hvp)

#         # if you want the sparse scaling you had before:
#         if args.sparse:
#             trace_est *= args.dim

#         # finally evaluate f
#         f_val = fn(x_i)
#         return f_val, trace_est, key

#     return fn_trace


# test_hess_trace_estimator()

def SineGordon_op(x, u, key: jax.Array) -> Tuple[Float[Array, "xt_dim"], jax.Array]:
    r"""
    .. math::
    \nabla u(x) + sin(u(x)) = g(x)
    """
    # run the Hessian‐trace estimator
    u_, u_xx, key = hess_trace(u)(x, key)
    return u_xx + jnp.sin(u_), key


coeffs_ = np.random.randn(1, args.dim)


def twobody_sol(x) -> Float[Array, "*batch"]:
    t1 = args.x_radius**2 - jnp.sum(x**2, -1)
    x1, x2 = x[..., :-1], x[..., 1:]
    t2 = coeffs_[:, :-1] * jnp.sin(x1 + jnp.cos(x2) + x2 * jnp.cos(x1))
    t2 = jnp.sum(t2, -1)
    u_exact = jnp.squeeze(t1 * t2)
    return u_exact

def twobody_lapl_analytical(x):
    coeffs = coeffs_[:, :-1]
    const_2 = 1
    u1 = 1 - np.sum(x**2)
    du1_dx = -2 * x
    d2u1_dx2 = -2

    x1, x2 = x[:-1], x[1:]
    coeffs = coeffs.reshape(-1)
    u2 = coeffs * jnp.sin(const_2 * (x1 + jnp.cos(x2) + x2 * jnp.cos(x1)))
    u2 = jnp.sum(u2)
    du2_dx_part1 = coeffs * jnp.cos(const_2 * (x1 + jnp.cos(x2) + x2 * jnp.cos(x1))) * \
            const_2 * (1 - x2 * jnp.sin(x1))
    du2_dx_part2 = coeffs * jnp.cos(const_2 * (x1 + jnp.cos(x2) + x2 * jnp.cos(x1))) * \
            const_2 * (-jnp.sin(x2) + jnp.cos(x1))
    du2_dx = jnp.zeros((args.dim,))
    du2_dx = du2_dx.at[:-1].add(du2_dx_part1)
    du2_dx = du2_dx.at[1:].add(du2_dx_part2)
    d2u2_dx2_part1 = -coeffs * jnp.sin(const_2 * (x1 + jnp.cos(x2) + x2 * jnp.cos(x1))) * \
        const_2**2 * (1 - x2 * jnp.sin(x1))**2 + \
        coeffs * const_2 * jnp.cos(const_2 * (x1 + jnp.cos(x2) + x2 * jnp.cos(x1))) * (- x2 * jnp.cos(x1))
    d2u2_dx2_part2 = -coeffs * jnp.sin(const_2 * (x1 + jnp.cos(x2) + x2 * jnp.cos(x1))) * \
        const_2**2 * (-jnp.sin(x2) + jnp.cos(x1))**2 + \
        coeffs * const_2 * jnp.cos(const_2 * (x1 + jnp.cos(x2) + x2 * jnp.cos(x1))) * \
            (-jnp.cos(x2))
    d2u2_dx2 = jnp.zeros((args.dim,))
    d2u2_dx2 = d2u2_dx2.at[:-1].add(d2u2_dx2_part1)
    d2u2_dx2 = d2u2_dx2.at[1:].add(d2u2_dx2_part2)
    ff = u1 * d2u2_dx2 + 2 * du1_dx * du2_dx + u2 * d2u1_dx2
    ff = jnp.sum(ff)
    u = (u1 * u2)
    return ff, u

def SineGordon_twobody_inhomo_exact(x):
    u_exact_lapl, u_exact = twobody_lapl_analytical(x)
    g_exact = u_exact_lapl + jnp.sin(u_exact)
    return g_exact

def SineGordon_res_fn(x, u, key) -> Float[Array, "xt_dim"]:
    r"""
    .. math::
    L u(x) = g(x)
    """
    Lu = SineGordon_op(x, u, key)
    g = SineGordon_twobody_inhomo_exact(x)
    return Lu - g

# -----------------------------------------------------------------------------
# Training state
# -----------------------------------------------------------------------------
class MambaTrainState(train_state.TrainState):
    rng: jax.Array


# -----------------------------------------------------------------------------
# Main
# -----------------------------------------------------------------------------
def main():
    # rng setup
    master_rng = jax.random.PRNGKey(args.SEED)
    rng_train, rng_test = jax.random.split(master_rng)

    # instantiate Bi-MAMBA
    mamba_cfg = MambaConfig(
        hidden_features       = args.hidden_features,
        expansion_factor      = args.expansion_factor,
        dt_rank               = args.dt_rank,
        activation            = args.activation,
        norm_type             = args.norm_type,
        mlp_layer             = args.mlp_layer,
        dense_expansion       = args.dense_expansion,
        complement            = args.complement,
        tie_in_proj           = args.tie_in_proj,
        tie_gate              = args.tie_gate,
        concatenate_fwd_rev   = args.concatenate_fwd_rev,
        radius                = args.x_radius,
        diagnostics           = DiagnosticsConfig(
            skip     = args.diag_skip,
            gate     = args.diag_gate,
            gated    = args.diag_gated,
            residual = args.diag_residual,
        ),
    )

    # And then proceed to instantiate your model as before:
    mamba = BidirectionalMamba(**vars(mamba_cfg))

    # initialize parameters on a dummy sequence
    rng_train, init_rng = jax.random.split(rng_train)
    x_dummy, rng = sample_domain_fn(batch_size=2,
                                    rng=init_rng,
                                    radius=args.x_radius,
                                    dim=args.dim,
                                    seq_len=args.seq_len,
                                    x_ordering=args.x_ordering,
                                    sampling_mode=args.sampling_mode)
    flax_vars = mamba.init(init_rng, x_dummy, train=True)
    
    # init optimizers
    lr = optax.linear_schedule(
        init_value=args.lr,
        end_value=args.lr * 1e-1,
        transition_steps=args.epochs,
        transition_begin=0,
    )
    
    # create optimizer + train state
    optimizer = optax.adam(lr)
    state = MambaTrainState.create(
        apply_fn=mamba.apply,
        params=flax_vars["params"],
        tx=optimizer,
        rng=rng_train,
    )

    # prepare test set (once)
    n_test_batches = args.N_test // args.test_batch_size
    test_seqs = []
    test_truths = []
    for _ in range(n_test_batches):
        rng_test, sample_rng = jax.random.split(rng_test)
        x_test_seq, _ = sample_domain_fn(args.test_batch_size,
                                        rng=sample_rng,
                                        radius=args.x_radius,
                                        dim=args.dim,
                                        seq_len=args.seq_len,
                                        x_ordering=args.x_ordering,
                                        sampling_mode=args.sampling_mode)
        # collapse batch & seq dims to analytical solver
        B, L, D = x_test_seq.shape
        x_flat = x_test_seq.reshape((B * L, D))
        y_flat = jax.vmap(twobody_sol)(x_flat)
        test_seqs.append(x_test_seq)
        test_truths.append(y_flat.reshape((B, L)))
    test_seqs = jnp.stack(test_seqs)       # (n_batches, B, L, D)
    test_truths = jnp.stack(test_truths)   # (n_batches, B, L)
    
    # flatten all test ground-truth values into a single vector
    y_true_all = test_truths.reshape(-1)  

    # L1 norm of the entire test set
    y_true_l1 = float(jnp.sum(jnp.abs(y_true_all)))     

    # L2 norm of the entire test set
    y_true_l2 = float(jnp.linalg.norm(y_true_all))   

    @jax.jit
    def train_step(state: MambaTrainState) -> MambaTrainState:
        # 1) split off one rng for sampling, one to carry forward
        batch_rng, next_rng = jax.random.split(state.rng)
        x_seq, batch_rng = sample_domain_fn(
            batch_size=args.rand_batch_size,
            rng=batch_rng,
            radius=args.x_radius,
            dim=args.dim,
            seq_len=args.seq_len,
            x_ordering=args.x_ordering,
            sampling_mode=args.sampling_mode
        )  # x_seq: (B, L, D)

        def loss_fn(params, rng):
            """
            Computes the mean-squared PDE residual loss for a batch given parameters.
            Returns:
            loss:       scalar MSE of all residuals
            new_rng:    PRNGKey to carry forward
            """
            # 1) split RNG for sampling vs. carry-forward
            batch_rng, new_rng = jax.random.split(rng)

            # 2) sample a fresh batch of input sequences
            x_seq, batch_rng = sample_domain_fn(
                batch_size=args.rand_batch_size,
                rng=batch_rng,
                radius=args.x_radius,
                dim=args.dim,
                seq_len=args.seq_len,
                x_ordering=args.x_ordering,
                sampling_mode=args.sampling_mode
            )  # x_seq shape: (B, L, D)

            # 3) helper: model output at time index l
            def y_at_l(x_i, l, full_seq):
                # replace the l-th entry of full_seq with x_i
                seq2 = lax.dynamic_update_slice(full_seq, x_i[None, :], (l, 0))
                # apply your Bi-MAMBA with the passed-in params
                y2 = mamba.apply({"params": params}, seq2[None, ...], train=True).squeeze(0)
                return y2[l]

            # 4) compute the vector of residuals for one sequence
            def residuals_for_one_sequence(full_seq, key):
                L = full_seq.shape[0]

                def one_step_res(l, x_l, key):
                    # build a u_fn that closes over `params` and this full_seq
                    def u_fn(xi):
                        return y_at_l(xi, l, full_seq)

                    # STDE Hessian-trace at x_l
                    y0, lap, key = hess_trace(u_fn)(x_l, key)

                    # analytic inhomogeneity
                    g0 = SineGordon_twobody_inhomo_exact(x_l)

                    return (lap + jnp.sin(y0) - g0), key

                # split into L subkeys, run across time steps
                keys = jax.random.split(key, L)
                resids, _ = jax.vmap(one_step_res, in_axes=(0, 0, 0), out_axes=(0, 0))(
                    jnp.arange(L), full_seq, keys
                )
                return resids, _

            # 5) vectorize over the B sequences in the batch
            outer_keys = jax.random.split(batch_rng, x_seq.shape[0])
            all_resids, _ = jax.vmap(
                residuals_for_one_sequence, in_axes=(0, 0), out_axes=(0, 0)
            )(x_seq, outer_keys)  # all_resids shape: (B, L)

            # 6) mean-squared residual loss
            loss = jnp.mean(all_resids ** 2)
            return loss, new_rng

        # inside your jitted train_step
        (loss, new_rng), grads = jax.value_and_grad(loss_fn, has_aux=True)(
            state.params, state.rng
        )
        state = state.apply_gradients(grads=grads, rng=new_rng)

        return state, loss, grads


    # inside main(), replace your training loop with something like:
    for step in tqdm(range(args.epochs), desc="training"):
        # let train_step now return (new_state, train_loss, grads)
        state, train_loss, grads = train_step(state)

        if step % args.eval_every == 0:
            # compute relative test errors
            l1_total, l2_total_sqr = 0., 0.
            for b in range(test_seqs.shape[0]):
                x_seq = test_seqs[b]
                y_true = test_truths[b]        # (B, L)
                y_pred = mamba.apply({"params": state.params}, x_seq, train=False) # .squeeze(-1)
                err = y_pred - y_true
                l1_total += jnp.sum(jnp.abs(err))
                l2_total_sqr += jnp.sum(err**2)
            l1_rel = float(l1_total / y_true_l1)
            l2_rel = float(jnp.sqrt(l2_total_sqr) / y_true_l2)

            # gradient norm
            grad_norm = jnp.sqrt(
                sum(jnp.vdot(g, g) for g in jax.tree_util.tree_leaves(grads))
            )

            print(f"step={step:4d} | "
                f"train_loss={train_loss:.3e} | "
                f"grad_norm={grad_norm:.3e} | "
                f"l1_rel={l1_rel:.3e} | "
                f"l2_rel={l2_rel:.3e}")
    
    # --- Final evaluation on the full test set ---
    print("\n=== Final evaluation on test set ===")
    l1_total, l2_total_sqr = 0.0, 0.0
    for b in range(test_seqs.shape[0]):
        x_seq = test_seqs[b]               # (B, L, D)
        y_true = test_truths[b]            # (B, L)

        y_pred = mamba.apply({"params": state.params}, x_seq, train=False)
        # drop trailing dim if present
        if y_pred.ndim == 3 and y_pred.shape[-1] == 1:
            y_pred = y_pred.squeeze(-1)

        err = y_pred - y_true
        l1_total       += jnp.sum(jnp.abs(err))
        l2_total_sqr   += jnp.sum(err**2)

    l1_rel = float(l1_total / y_true_l1)
    l2_rel = float(jnp.sqrt(l2_total_sqr) / y_true_l2)
    print(f"Final → l1_rel={l1_rel:.3e} | l2_rel={l2_rel:.3e}")


    def plot_sine_gordon_solution(x_flat, u_true, u_pred,
                                dim: int = None,
                                xlabel: str = 'x',
                                ylabel: str = 'u',
                                cmap: str = 'viridis'):
        """
        Plot true vs. predicted Sine–Gordon solutions.
        If the embedding dimension D > 2, only the first two dims are used for plotting.
        
        Args:
        x_flat:   array of shape (N, D) or (N,) for 1D
        u_true:   array of shape (N,)
        u_pred:   array of shape (N,)
        dim:      override spatial dimension (1 or 2). If None, inferred from x_flat.
        """
        # Infer dimension if not provided
        if dim is None:
            if x_flat.ndim == 1:
                D = 1
            else:
                D = x_flat.shape[1]
        else:
            D = dim

        # Cap at 2 dimensions for plotting
        if D > 2:
            print(f"Warning: embedding dim={D} > 2; plotting only first two dims.")
            D = 2

        # 1D plot
        if D == 1:
            # collapse to 1D coordinate
            x = x_flat if x_flat.ndim == 1 else x_flat[:, 0]
            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(10, 4))
            ax1.plot(x, u_true, '.', label='true')
            ax1.set_title('True u(x)')
            ax1.set_xlabel(xlabel)
            ax1.set_ylabel(ylabel)
            ax2.plot(x, u_pred, '.', label='pred', color='C1')
            ax2.set_title('Predicted u(x)')
            ax2.set_xlabel(xlabel)
            ax2.set_ylabel(ylabel)

        # 2D scatter with shared colorbar
        elif D == 2:
            # pick first two coords
            xi = x_flat[:, 0]
            yi = x_flat[:, 1]

            # common color limits
            vmin = min(np.min(u_true), np.min(u_pred))
            vmax = max(np.max(u_true), np.max(u_pred))

            fig, (ax1, ax2) = plt.subplots(
                1, 2, figsize=(12, 5), subplot_kw={'aspect': 'equal'}
            )

            sc1 = ax1.scatter(xi, yi,
                            c=u_true, cmap=cmap,
                            vmin=vmin, vmax=vmax,
                            s=20)
            ax1.set_title('True u')
            ax1.set_xlabel('dim0')
            ax1.set_ylabel('dim1')

            sc2 = ax2.scatter(xi, yi,
                            c=u_pred, cmap=cmap,
                            vmin=vmin, vmax=vmax,
                            s=20)
            ax2.set_title('Predicted u')
            ax2.set_xlabel('dim0')
            ax2.set_ylabel('dim1')

            # one shared colorbar anchored to the bottom outside the x axis
            cbar = fig.colorbar(sc1, ax=[ax1, ax2],
                                shrink=0.8, pad=0.02,
                                label=ylabel,
                                orientation='vertical',
                                location='right',
                                )

            # plt.tight_layout()

        else:
            raise ValueError("plot only supports 1D or 2D embeddings for visualization")

        # plt.tight_layout()
        plt.show()

    def plot_all_sine_gordon_solutions(test_seqs, test_truths, params, mamba, dim=None,
                                    xlabel='x', ylabel='u', cmap='viridis'):
        """
        Run the model over every test batch, flatten, and plot true vs. predicted.
        
        Args:
        test_seqs:   array (n_batches, B, L, D)
        test_truths: array (n_batches, B, L)
        params:      your trained params to pass into mamba.apply
        mamba:       your BidirectionalMamba instance
        dim:         spatial dimension (if None, inferred from D)
        """
        # flatten batches
        n_batches, B, L, D = test_seqs.shape
        if dim is None:
            dim = D

        # reshape to (n_batches*B, L, D) for a big batch
        seqs_flat = test_seqs.reshape((n_batches * B, L, D))
        # run model in one go
        u_pred_seq = mamba.apply({"params": params},
                                seqs_flat,
                                train=False)      # → (n_batches*B, L, 1) or (nB, L)
        
        # flatten spatial points and truths
        x_flat = test_seqs.reshape((n_batches * B * L, D))
        u_true = test_truths.reshape((n_batches * B * L,))
        u_pred = u_pred_seq.reshape((n_batches * B * L,))

        # convert to NumPy for plotting
        x_flat_np = np.array(x_flat)
        u_true_np  = np.array(u_true)
        u_pred_np  = np.array(u_pred)

        # now call the existing plot fn
        plot_sine_gordon_solution(x_flat_np,
                                u_true_np,
                                u_pred_np,
                                dim=dim,
                                xlabel=xlabel,
                                ylabel=ylabel,
                                cmap=cmap)
    
    plot_all_sine_gordon_solutions(test_seqs,
                               test_truths,
                               state.params,
                               mamba,
                               dim=args.dim)
    def visualize_sequences(x_seq, x_ordering="none"):
        """
        Scatter‐plot each 2D “sequence” in x_seq, coloring by sequence index.
        If x_ordering="radius" or "coordinate", also draw lines to show the ordering.

        Args:
        x_seq:       numpy array of shape (B, L, 2)
        x_ordering:  "none", "coordinate", or "radial"
        """
        B, L, D = x_seq.shape
        assert D == 2, "visualize_sequences only supports 2D inputs"

        cmap = plt.get_cmap("tab10")
        plt.figure(figsize=(6,6))

        for b in range(B):
            pts = x_seq[b]  # (L, 2)

            # apply ordering if requested
            if x_ordering == "coordinate":
                idx = np.argsort(pts[:, 0])
                pts = pts[idx]
            elif x_ordering == "radial":
                idx = np.argsort(np.linalg.norm(pts, axis=1))
                pts = pts[idx]
            # else "none": leave as is

            color = cmap(b % 10)
            plt.scatter(pts[:,0], pts[:,1], color=color, s=30, label=f"seq {b}")

            # if ordered, draw lines to reveal the path
            if x_ordering in ("coordinate", "radial"):
                plt.plot(pts[:,0], pts[:,1], color=color, alpha=0.5)

        plt.title(f"Sequences on 2D ball (ordering={x_ordering})")
        plt.gca().set_aspect("equal", "box")
        plt.xlabel("x₀")
        plt.ylabel("x₁")
        plt.legend(bbox_to_anchor=(1.05, 1), loc="upper left")
        plt.tight_layout()
        plt.show()
    visualize_sequences(test_seqs[0], x_ordering=args.x_ordering)



if __name__ == "__main__":
    main()
