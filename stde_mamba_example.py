"""
Example script demonstrating how to use the STDE-compatible Mamba model with
jax.experimental.jet for Taylor automatic differentiation.

Note: The current implementation provides a simplified version of the Mamba model
that is compatible with jax.experimental.jet. The full SSM scan operation is not
yet fully implemented in a way that works with jet, but the simplified model
provides a starting point for using Mamba-like architectures with Taylor
automatic differentiation.
"""

import jax
import jax.numpy as jnp
from jax.experimental import jet
from functools import partial

from mamba import MambaConfig, DiagnosticsConfig, sample_domain_fn
from stde_mamba import ssm_loop_scan, BidirectionalSTDEMamba
from mamba_utils import create_mamba_model


def main():
    # Set up parameters
    B, L, D = 2, 1, 10  # Batch size, sequence length, input dimension

    # Create a random input
    key = jax.random.PRNGKey(0)
    x, _, key = sample_domain_fn(B, D, 1.0, key)

    # Create a Mamba model configuration
    model_config = MambaConfig(
        hidden_features=32,
        expansion_factor=2.0,
        dt_rank='auto',
        activation='gelu',
        norm_type='layer',
        mlp_layer=True,
        dense_expansion=4,
        complement=True,
        tie_in_proj=True,
        tie_gate=True,
        concatenate_fwd_rev=True,
        diagnostics=DiagnosticsConfig()
    )

    # Create an STDE-compatible Mamba model
    model = create_mamba_model(model_config, use_stde_compatible=True)

    # Initialize the model
    params = model.init(key, x)

    # Define a function that applies the model
    def model_fn(x_input):
        return model.apply(params, x_input)

    # Create perturbation vectors for Taylor automatic differentiation
    v = jnp.ones_like(x)  # First-order perturbation
    zeros = jnp.zeros_like(x)  # Second-order perturbation (zero)

    # Apply Taylor automatic differentiation using jax.experimental.jet
    print("Running Taylor automatic differentiation...")
    primals, (tangents, _) = jet.jet(model_fn, (x,), ((v, zeros),))

    print(f"Input shape: {x.shape}")
    print(f"Output shape: {primals.shape}")
    print(f"First-order derivatives shape: {tangents.shape}")

    print("\nPrimal output (first few values):")
    print(primals[:2])

    print("\nFirst-order derivatives (first few values):")
    print(tangents[:2])

    # Demonstrate computing a Hessian-vector product using jet
    def compute_hvp(fn, x, v):
        """Compute a Hessian-vector product using jet."""
        _, (_, hvp) = jet.jet(fn, (x,), ((v, jnp.zeros_like(v)),))
        return hvp

    # Compute the Hessian-vector product
    print("\nComputing Hessian-vector product...")
    hvp = compute_hvp(model_fn, x, v)

    print(f"Hessian-vector product shape: {hvp.shape}")
    print("Hessian-vector product (first few values):")
    print(hvp[:2])

    # Demonstrate computing the trace of the Hessian using jet
    def compute_hessian_trace(fn, x, batch_size=10):
        """Compute the trace of the Hessian using jet and random projections."""
        dim = x.shape[-1]
        key = jax.random.PRNGKey(0)

        # Generate random vectors for Hutchinson's estimator
        rand_vecs = jax.random.normal(key, shape=(batch_size,) + x.shape)

        # Compute Hessian-vector products
        hvps = jax.vmap(lambda v: compute_hvp(fn, x, v))(rand_vecs)

        # Compute the trace estimate using Hutchinson's estimator
        trace_est = jnp.mean(jnp.sum(rand_vecs * hvps, axis=tuple(range(1, hvps.ndim))))

        return trace_est

    # Compute the trace of the Hessian
    print("\nComputing trace of the Hessian...")
    trace = compute_hessian_trace(model_fn, x)

    print(f"Trace of the Hessian: {trace}")


if __name__ == "__main__":
    main()
