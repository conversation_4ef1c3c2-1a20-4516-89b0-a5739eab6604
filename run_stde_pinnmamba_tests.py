#!/usr/bin/env python
"""
Script to run the STDE-PINNMamba model tests.
"""

import os
import sys
import subprocess

def main():
    """Run the STDE-PINNMamba model tests."""
    # Define the command to run
    cmd = [
        "python", "stde_pinnmamba.py"
    ]
    
    # Print the command
    print("Running command:")
    print(" ".join(cmd))
    
    # Run the command
    subprocess.run(cmd)

if __name__ == "__main__":
    # Make sure we're in the right conda environment
    if "phd" not in os.environ.get("CONDA_DEFAULT_ENV", ""):
        print("Please activate the 'phd' conda environment before running this script.")
        print("Run: conda activate phd")
        sys.exit(1)
    
    # Run the main function
    main()
