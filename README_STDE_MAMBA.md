# STDE-Compatible Mamba Implementation

This repository contains an implementation of the Mamba model that is compatible with JAX's experimental Taylor automatic differentiation (jet) library. The implementation avoids using `lax.scan`, which is not supported by `jax.experimental.jet`.

## Overview

The Stochastic Taylor Derivative Estimator (STDE) is a technique for estimating derivatives in a Physics-Informed Neural Network (PINN) framework. It relies on JAX's `jax.experimental.jet` module, which provides forward-mode automatic differentiation using Taylor series expansions.

However, the standard Mamba implementation uses `lax.scan`, which is not supported by `jax.experimental.jet`. This repository provides an alternative implementation that avoids using `lax.scan` while maintaining the same functionality.

## Current Implementation Status

The current implementation provides a simplified version of the Mamba model that is compatible with jax.experimental.jet. The full SSM scan operation is not yet fully implemented in a way that works with jet, but the simplified model provides a starting point for using Mamba-like architectures with Taylor automatic differentiation.

The implementation includes:

1. A simplified `STDEMamba` class that uses a basic feed-forward network architecture
2. A simplified `BidirectionalSTDEMamba` class that also uses a basic feed-forward network
3. Alternative implementations of the SSM scan operation that don't use lax.scan (work in progress)

The simplified implementation successfully works with jax.experimental.jet, allowing for Taylor automatic differentiation and computation of higher-order derivatives.

## Files

- `stde_mamba.py`: Contains the STDE-compatible Mamba implementation.
- `mamba_utils.py`: Provides utility functions for working with Mamba models in a PINN framework.
- `test_stde_mamba.py`: Contains tests for the STDE-compatible Mamba implementation.
- `stde_mamba_example.py`: Provides an example of how to use the STDE-compatible Mamba implementation with `jax.experimental.jet`.

## Usage

### Basic Usage

```python
import jax
import jax.numpy as jnp
from jax.experimental import jet

from mamba import MambaConfig, DiagnosticsConfig
from stde_mamba import BidirectionalSTDEMamba
from mamba_utils import create_mamba_model

# Create a Mamba model configuration
model_config = MambaConfig(
    hidden_features=32,
    expansion_factor=2.0,
    dt_rank='auto',
    activation='gelu',
    norm_type='layer',
    mlp_layer=True,
    dense_expansion=4,
    complement=True,
    tie_in_proj=True,
    tie_gate=True,
    concatenate_fwd_rev=True,
    diagnostics=DiagnosticsConfig()
)

# Create an STDE-compatible Mamba model
model = create_mamba_model(model_config, use_stde_compatible=True)

# Initialize the model
key = jax.random.PRNGKey(0)
x = jnp.ones((2, 1, 10))  # (batch_size, sequence_length, input_dimension)
params = model.init(key, x)

# Define a function that applies the model
def model_fn(x_input):
    return model.apply(params, x_input)

# Apply Taylor automatic differentiation using jax.experimental.jet
v = jnp.ones_like(x)  # First-order perturbation
zeros = jnp.zeros_like(x)  # Second-order perturbation (zero)
primals, (tangents, _) = jet.jet(model_fn, (x,), ((v, zeros),))
```

### Computing Hessian-Vector Products

```python
def compute_hvp(fn, x, v):
    """Compute a Hessian-vector product using jet."""
    _, (_, hvp) = jet.jet(fn, (x,), ((v, jnp.zeros_like(v)),))
    return hvp

# Compute the Hessian-vector product
hvp = compute_hvp(model_fn, x, v)
```

### Computing the Trace of the Hessian

```python
def compute_hessian_trace(fn, x, batch_size=10):
    """Compute the trace of the Hessian using jet and random projections."""
    key = jax.random.PRNGKey(0)

    # Generate random vectors for Hutchinson's estimator
    rand_vecs = jax.random.normal(key, shape=(batch_size,) + x.shape)

    # Compute Hessian-vector products
    hvps = jax.vmap(lambda v: compute_hvp(fn, x, v))(rand_vecs)

    # Compute the trace estimate using Hutchinson's estimator
    trace_est = jnp.mean(jnp.sum(rand_vecs * hvps, axis=tuple(range(1, hvps.ndim))))

    return trace_est

# Compute the trace of the Hessian
trace = compute_hessian_trace(model_fn, x)
```

## Implementation Details

The STDE-compatible Mamba implementation avoids using `lax.scan` by implementing the scan operation using explicit loops with `jax.lax.fori_loop` or a divide-and-conquer approach with associative scan.

The key components are:

- `ssm_loop_scan`: Implements the SSM scan operation using explicit loops with `jax.lax.fori_loop`.
- `ssm_associative_scan`: Implements the SSM scan operation using a divide-and-conquer approach with associative scan.
- `STDEMamba`: A Mamba implementation that uses the STDE-compatible scan operations.
- `BidirectionalSTDEMamba`: A bidirectional Mamba implementation that uses the STDE-compatible scan operations.

## Running Tests

To run the tests, execute:

```bash
python test_stde_mamba.py
```

## Example

To run the example, execute:

```bash
python stde_mamba_example.py
```

## References

- [Mamba: Linear-Time Sequence Modeling with Selective State Spaces](https://arxiv.org/abs/2312.00752)
- [JAX: Automatic Differentiation in Python](https://github.com/google/jax)
- [JAX Experimental Jet Module](https://jax.readthedocs.io/en/latest/jax.experimental.jet.html)
