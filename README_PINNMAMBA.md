# PINNMamba for Sine-Gordon Equation

This repository contains an implementation of Physics-Informed Neural Networks with Mamba architecture (PINNMamba) for solving the Sine-Gordon equation. The implementation follows the sequence-to-sequence, encoder-only model approach from the PINNMamba paper.

## Overview

The Sine-Gordon equation is a nonlinear hyperbolic partial differential equation that appears in various areas of physics, including quantum field theory, nonlinear optics, and condensed matter physics. The equation is given by:

$$u_{tt} - u_{xx} + \sin(u) = 0$$

where $u$ is the unknown function, $u_{tt}$ is the second derivative with respect to time, and $u_{xx}$ is the second derivative with respect to space.

This implementation uses a PINNMamba model to solve the Sine-Gordon equation. The PINNMamba architecture combines the strengths of Physics-Informed Neural Networks (PINNs) with the Mamba architecture, which is a state-space model that can efficiently process sequential data.

## Files

- `pinnmamba.py`: Contains the PINNMamba model implementation and utility functions for creating sequence data.
- `train_pinnmamba.py`: Contains the training script for the PINNMamba model.
- `run_pinnmamba_tests.py`: <PERSON>rip<PERSON> to run the PINNMamba model tests.
- `run_pinnmamba_training.py`: <PERSON><PERSON><PERSON> to run the PINNMamba model training.

## Usage

### Prerequisites

Make sure you have the `phd` conda environment activated:

```bash
conda activate phd
```

### Running the Tests

To run the PINNMamba model tests, execute:

```bash
python run_pinnmamba_tests.py
```

This will test the PINNMamba model implementation and the sequence data creation functions.

### Training the Model

To train the PINNMamba model on the Sine-Gordon equation, execute:

```bash
python run_pinnmamba_training.py
```

This will train the model with the default parameters and save the results to the `outputs` directory.

### Customizing the Training

You can customize the training by modifying the parameters in `run_pinnmamba_training.py` or by directly running `train_pinnmamba.py` with custom arguments:

```bash
python train_pinnmamba.py --hidden_dim 128 --num_layers 6 --epochs 200
```

## Model Architecture

The PINNMamba model consists of the following components:

1. **Input Embedding**: Projects the input coordinates (spatial and temporal) to a higher-dimensional space.
2. **Encoder**: Processes the embedded input using multiple layers of Bidirectional Mamba.
3. **Output Projection**: Projects the encoded representation to the output dimension.

The model is trained to satisfy:
- The Sine-Gordon PDE: $u_{tt} - u_{xx} + \sin(u) = 0$
- Initial conditions: $u(x, 0) = 0$ and $u_t(x, 0) = 0$
- Boundary conditions: $u(x, t) = 0$ for $\|x\| = \text{radius}$

## Sequence-to-Sequence Approach

Following the PINNMamba paper, this implementation uses a sequence-to-sequence approach:

1. The input data is organized into sequences of points in the spatio-temporal domain.
2. Each sequence is processed by the PINNMamba model to produce a sequence of outputs.
3. The model is trained to satisfy the PDE, initial conditions, and boundary conditions at all points in the sequence.

This approach allows the model to capture the dynamics of the PDE across the entire domain.

## Parameters

The model and training parameters can be customized through command-line arguments:

### Model Parameters
- `--in_dim`: Input dimension (x_dim + 1 for time)
- `--out_dim`: Output dimension
- `--hidden_dim`: Hidden dimension
- `--num_layers`: Number of Mamba layers
- `--hidden_d_ff`: Hidden feed-forward dimension
- `--heads`: Number of attention heads
- `--dropout_rate`: Dropout rate

### Data Parameters
- `--n_x`: Number of spatial points
- `--n_t`: Number of temporal points
- `--x_radius`: Radius of spatial domain
- `--t_max`: Maximum time
- `--batch_size`: Batch size
- `--seq_len`: Sequence length

### Training Parameters
- `--lr`: Learning rate
- `--weight_decay`: Weight decay
- `--epochs`: Number of epochs
- `--eval_every`: Evaluate every N epochs
- `--save_every`: Save model every N epochs
- `--seed`: Random seed
- `--output_dir`: Output directory

### PDE Parameters
- `--pde_weight`: Weight for PDE loss
- `--ic_weight`: Weight for initial condition loss
- `--bc_weight`: Weight for boundary condition loss

## References

- [PINNMamba: Physics-Informed Neural Networks with Mamba Architecture](https://arxiv.org/abs/2403.01131)
- [Mamba: Linear-Time Sequence Modeling with Selective State Spaces](https://arxiv.org/abs/2312.00752)
- [Physics-Informed Neural Networks](https://arxiv.org/abs/1711.10561)
