"""
Simplified PINNMamba: Physics-Informed Neural Networks with Mamba architecture for the Sine-Gordon equation.

This implementation follows the sequence-to-sequence, encoder-only model approach from the PINNMamba paper,
but uses a simplified architecture to ensure compatibility with JAX's tracer system.
"""

import jax
import jax.numpy as jnp
import flax.linen as nn
from functools import partial
from typing import Callable

from mamba import MambaConfig, DiagnosticsConfig, sample_domain_fn


class SimpleMambaBlock(nn.Module):
    """
    A simplified Mamba block that processes sequence data.
    """
    hidden_dim: int

    @nn.compact
    def __call__(self, x):
        """
        Apply the Mamba block to the input sequence.

        Args:
            x: Input tensor of shape (batch_size, seq_len, hidden_dim)

        Returns:
            Output tensor of shape (batch_size, seq_len, hidden_dim)
        """
        # Layer normalization
        x = nn.LayerNorm()(x)

        # Project to higher dimension
        x_proj = nn.Dense(features=2 * self.hidden_dim)(x)
        x_proj = nn.gelu(x_proj)

        # Sequence modeling with attention-like mechanism
        # Split into query and key
        B, L, D = x_proj.shape
        query, key = jnp.split(x_proj, 2, axis=-1)

        # Compute attention scores
        scores = jnp.einsum('bld,bmd->blm', query, key) / jnp.sqrt(D / 2)

        # Apply softmax to get attention weights
        weights = jax.nn.softmax(scores, axis=-1)

        # Apply attention
        x_seq = jnp.einsum('blm,bmd->bld', weights, x_proj)

        # Project back to original dimension
        x_out = nn.Dense(features=self.hidden_dim)(x_seq)

        # Residual connection
        return x + x_out


class PINNMambaEncoder(nn.Module):
    """
    Encoder module for PINNMamba architecture.

    This module processes input sequences using multiple SimpleMambaBlock layers.
    """
    hidden_dim: int
    num_layers: int

    @nn.compact
    def __call__(self, x):
        """
        Apply the encoder to the input sequence.

        Args:
            x: Input tensor of shape (batch_size, seq_len, hidden_dim)

        Returns:
            Output tensor of shape (batch_size, seq_len, hidden_dim)
        """
        # Apply multiple layers of SimpleMambaBlock
        for _ in range(self.num_layers):
            x = SimpleMambaBlock(hidden_dim=self.hidden_dim)(x)

        # Final layer normalization
        x = nn.LayerNorm()(x)

        return x


class SimplePINNMamba(nn.Module):
    """
    Simplified PINNMamba model for solving PDEs using sequence-to-sequence approach.

    This model follows the encoder-only architecture from the PINNMamba paper.
    """
    in_dim: int
    out_dim: int
    hidden_dim: int
    num_layers: int

    @nn.compact
    def __call__(self, x, t, train: bool = False):
        """
        Forward pass of the PINNMamba model.

        Args:
            x: Spatial coordinates tensor of shape (batch_size, seq_len, x_dim)
            t: Time coordinates tensor of shape (batch_size, seq_len, 1)
            train: Whether the model is in training mode

        Returns:
            Output tensor of shape (batch_size, seq_len, out_dim)
        """
        # Concatenate spatial and temporal coordinates
        inputs = jnp.concatenate([x, t], axis=-1)  # (batch_size, seq_len, in_dim)

        # Input embedding
        x = nn.Dense(features=self.hidden_dim, name="input_embedding")(inputs)

        # Apply encoder
        encoder = PINNMambaEncoder(
            hidden_dim=self.hidden_dim,
            num_layers=self.num_layers
        )
        x = encoder(x)

        # Output projection
        x = nn.Dense(features=self.hidden_dim)(x)
        x = nn.gelu(x)
        x = nn.Dense(features=self.out_dim, name="output_projection")(x)

        return x


def create_sequence_data(x_points, t_points, batch_size=32, seq_len=128):
    """
    Create sequence data for training PINNMamba.

    Args:
        x_points: Spatial points of shape (n_x, x_dim)
        t_points: Temporal points of shape (n_t, 1)
        batch_size: Number of sequences in a batch
        seq_len: Length of each sequence

    Returns:
        Batched sequences of shape (batch_size, seq_len, x_dim + 1)
    """
    n_x = x_points.shape[0]
    n_t = t_points.shape[0]
    x_dim = x_points.shape[1]

    # Create random indices for sampling
    key = jax.random.PRNGKey(0)
    key, subkey1, subkey2 = jax.random.split(key, 3)

    # Initialize batch arrays
    x_batch = jnp.zeros((batch_size, seq_len, x_dim))
    t_batch = jnp.zeros((batch_size, seq_len, 1))

    for i in range(batch_size):
        # For each batch, create a sequence by sampling random points
        x_indices = jax.random.randint(subkey1, (seq_len,), 0, n_x)
        t_indices = jax.random.randint(subkey2, (seq_len,), 0, n_t)

        # Sample points
        x_seq = x_points[x_indices]
        t_seq = t_points[t_indices]

        # Store in batch
        x_batch = x_batch.at[i].set(x_seq)
        t_batch = t_batch.at[i].set(t_seq)

        # Update keys for next batch
        key, subkey1, subkey2 = jax.random.split(key, 3)

    return x_batch, t_batch


def create_sine_gordon_data(n_x=100, n_t=50, x_radius=1.0, t_max=1.0, batch_size=32, seq_len=128):
    """
    Create data for the Sine-Gordon equation.

    Args:
        n_x: Number of spatial points
        n_t: Number of temporal points
        x_radius: Radius of the spatial domain
        t_max: Maximum time
        batch_size: Number of sequences in a batch
        seq_len: Length of each sequence

    Returns:
        Batched sequences for training
    """
    # Create spatial points in a ball
    key = jax.random.PRNGKey(0)
    x_points, _, key = sample_domain_fn(n_x, 2, x_radius, key)
    x_points = x_points.reshape(n_x, 2)  # Remove sequence dimension

    # Create temporal points
    t_points = jnp.linspace(0, t_max, n_t).reshape(n_t, 1)

    # Create sequence data
    return create_sequence_data(x_points, t_points, batch_size, seq_len)


@partial(jax.jit, static_argnames=('u_fn',))
def sine_gordon_pde(x, t, u_fn: Callable):
    """
    Compute the Sine-Gordon PDE residual using a simplified approach.

    Args:
        x: Spatial coordinates tensor of shape (batch_size, seq_len, x_dim)
        t: Time coordinates tensor of shape (batch_size, seq_len, 1)
        u_fn: Function that computes u(x, t)

    Returns:
        PDE residual
    """
    # Compute u and its value
    u_val = u_fn(x, t)

    # Use finite differences to approximate derivatives
    # Small step size for finite differences
    h = 0.01

    # Time derivatives
    t_plus = t + h
    t_minus = t - h
    u_plus_t = u_fn(x, t_plus)
    u_minus_t = u_fn(x, t_minus)

    # First time derivative: central difference
    u_t = (u_plus_t - u_minus_t) / (2 * h)

    # Second time derivative: central difference
    u_tt = (u_plus_t - 2 * u_val + u_minus_t) / (h * h)

    # Spatial derivatives (for each dimension)
    u_xx = jnp.zeros_like(u_val)

    # For each spatial dimension
    for d in range(x.shape[-1]):
        # Create shifted coordinates
        x_plus = x.at[:, :, d].add(h)
        x_minus = x.at[:, :, d].add(-h)

        # Compute function values at shifted coordinates
        u_plus_x = u_fn(x_plus, t)
        u_minus_x = u_fn(x_minus, t)

        # Second spatial derivative: central difference
        u_xx += (u_plus_x - 2 * u_val + u_minus_x) / (h * h)

    # Compute the Sine-Gordon PDE residual: u_tt - u_xx + sin(u) = 0
    residual = u_tt - u_xx + jnp.sin(u_val)

    return residual


def test_simple_pinnmamba_model():
    """Test the SimplePINNMamba model."""
    print("\n=== Testing SimplePINNMamba Model ===")

    # Test parameters
    batch_size = 4
    seq_len = 10
    in_dim = 3  # 2D spatial + 1D temporal
    out_dim = 1
    hidden_dim = 32
    num_layers = 2

    print(f"Test parameters: batch_size={batch_size}, seq_len={seq_len}, in_dim={in_dim}, out_dim={out_dim}")
    print(f"Model config: hidden_dim={hidden_dim}, num_layers={num_layers}")

    # Initialize model
    model = SimplePINNMamba(
        in_dim=in_dim,
        out_dim=out_dim,
        hidden_dim=hidden_dim,
        num_layers=num_layers
    )
    print("Model initialized successfully")

    # Create dummy inputs
    key = jax.random.PRNGKey(0)
    key, subkey1, subkey2 = jax.random.split(key, 3)
    x = jax.random.normal(subkey1, (batch_size, seq_len, in_dim-1))
    t = jax.random.normal(subkey2, (batch_size, seq_len, 1))
    print(f"Input shapes: x={x.shape}, t={t.shape}")

    # Initialize parameters
    variables = model.init(key, x, t)
    print("Parameters initialized successfully")

    # Run forward pass
    output = model.apply(variables, x, t)
    print(f"Forward pass completed successfully")
    print(f"Output shape: {output.shape}")
    print(f"Output sample: {output[0, 0]}")

    # Test output shape
    expected_shape = (batch_size, seq_len, out_dim)
    assert output.shape == expected_shape, f"Expected output shape {expected_shape}, but got {output.shape}"
    print(f"Output shape test passed: {output.shape} matches expected {expected_shape}")

    # Test output type
    assert isinstance(output, jnp.ndarray), f"Expected output to be jnp.ndarray, but got {type(output)}"
    print(f"Output type test passed: {type(output).__name__}")

    # Test that output contains no NaN values
    has_nans = jnp.any(jnp.isnan(output))
    assert not has_nans, "Output contains NaN values"
    print(f"NaN check passed: Output contains no NaN values")

    print("SimplePINNMamba model test passed!")


def test_sequence_data_creation():
    """Test the sequence data creation functions."""
    print("\n=== Testing Sequence Data Creation ===")

    # Test parameters
    n_x = 100
    n_t = 50
    x_radius = 1.0
    t_max = 1.0
    batch_size = 8
    seq_len = 16

    print(f"Test parameters: n_x={n_x}, n_t={n_t}, batch_size={batch_size}, seq_len={seq_len}")

    # Create data
    x_batch, t_batch = create_sine_gordon_data(
        n_x=n_x,
        n_t=n_t,
        x_radius=x_radius,
        t_max=t_max,
        batch_size=batch_size,
        seq_len=seq_len
    )
    print("Data created successfully")

    # Check shapes
    assert x_batch.shape == (batch_size, seq_len, 2), f"Expected x_batch shape (batch_size, seq_len, 2), got {x_batch.shape}"
    assert t_batch.shape == (batch_size, seq_len, 1), f"Expected t_batch shape (batch_size, seq_len, 1), got {t_batch.shape}"
    print(f"Shape test passed: x_batch={x_batch.shape}, t_batch={t_batch.shape}")

    # Check values
    assert jnp.all(jnp.linalg.norm(x_batch, axis=-1) <= x_radius), "Some x values are outside the domain radius"
    assert jnp.all((t_batch >= 0) & (t_batch <= t_max)), "Some t values are outside the time range"
    print(f"Value range test passed")

    # Print sample data
    print(f"Sample x values: {x_batch[0, 0:2]}")
    print(f"Sample t values: {t_batch[0, 0:2]}")

    print("Sequence data creation test passed!")


def tabulate_simple_pinnmamba_model():
    """Tabulate the SimplePINNMamba model architecture."""
    print("\n=== Tabulating SimplePINNMamba Model Architecture ===")

    # Test parameters
    batch_size = 4
    seq_len = 10
    in_dim = 3
    out_dim = 1
    hidden_dim = 32
    num_layers = 2

    print(f"Model parameters: in_dim={in_dim}, out_dim={out_dim}, hidden_dim={hidden_dim}, num_layers={num_layers}")

    # Create dummy inputs
    key = jax.random.PRNGKey(0)
    key, subkey1, subkey2 = jax.random.split(key, 3)
    x = jax.random.normal(subkey1, (batch_size, seq_len, in_dim-1))
    t = jax.random.normal(subkey2, (batch_size, seq_len, 1))
    print(f"Input shapes: x={x.shape}, t={t.shape}")

    # Initialize model
    model = SimplePINNMamba(
        in_dim=in_dim,
        out_dim=out_dim,
        hidden_dim=hidden_dim,
        num_layers=num_layers
    )
    print("Model initialized successfully")

    # Print the tabulated model summary
    print("\nDetailed Model Architecture:")
    print(nn.tabulate(
        model,
        rngs={"params": jax.random.PRNGKey(0)},
        mutable=['params', 'batch_stats'],
    )(x, t, train=True))

    print("\nModel tabulation completed successfully!")


if __name__ == "__main__":
    print("\n===================================================")
    print("RUNNING SIMPLE PINNMAMBA MODEL TESTS")
    print("===================================================")

    test_simple_pinnmamba_model()
    test_sequence_data_creation()
    tabulate_simple_pinnmamba_model()

    print("\n===================================================")
    print("ALL TESTS COMPLETED SUCCESSFULLY!")
    print("===================================================")
