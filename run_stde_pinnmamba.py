#!/usr/bin/env python
"""
Script to run the STDE-PINNMamba model training.
"""

import os
import sys
import subprocess

def main():
    """Run the STDE-PINNMamba model training."""
    # Define the command to run
    cmd = [
        "python", "train_stde_pinnmamba.py",
        "--in_dim", "3",
        "--out_dim", "1",
        "--hidden_dim", "64",
        "--num_layers", "4",
        "--stde_order", "2",
        "--use_stde",  # Use STDE for computing derivatives
        "--n_x", "100",
        "--n_t", "10",
        "--x_radius", "1.0",
        "--t_max", "1.0",
        "--batch_size", "4",
        "--seq_len", "8",
        "--lr", "1e-3",
        "--weight_decay", "1e-5",
        "--epochs", "50",
        "--eval_every", "5",
        "--save_every", "10",
        "--seed", "42",
        "--output_dir", "outputs_stde",
        "--pde_weight", "1.0",
        "--ic_weight", "1.0",
        "--bc_weight", "1.0"
    ]
    
    # Print the command
    print("Running command:")
    print(" ".join(cmd))
    
    # Run the command
    subprocess.run(cmd)

if __name__ == "__main__":
    # Make sure we're in the right conda environment
    if "phd" not in os.environ.get("CONDA_DEFAULT_ENV", ""):
        print("Please activate the 'phd' conda environment before running this script.")
        print("Run: conda activate phd")
        sys.exit(1)
    
    # Run the main function
    main()
